// Simple test to verify our API endpoints work
const fs = require('fs');
const path = require('path');

// Mock database data for testing
const mockDbData = {
  trades: [
    {
      id: 1,
      from: 'testuser.wam',
      quantity: '100.0000 WAX',
      memo: 'test trade',
      status: 'completed',
      timestamp: new Date().toISOString(),
      transaction_id: 'abc123def456'
    },
    {
      id: 2,
      from: 'user2.wam',
      quantity: '50.0000 WAX',
      memo: 'another trade',
      status: 'pending',
      timestamp: new Date(Date.now() - 60000).toISOString(),
      transaction_id: 'def456ghi789'
    },
    {
      id: 3,
      from: 'user3.wam',
      quantity: '200.0000 WAX',
      memo: 'large trade',
      status: 'completed',
      timestamp: new Date(Date.now() - 120000).toISOString(),
      transaction_id: 'ghi789jkl012'
    }
  ],
  memos: [
    {
      memo: 'blocked_memo',
      is_blocked: true,
      block_reason: 'spam',
      created_at: new Date().toISOString()
    }
  ],
  pending_payouts: [],
  failed_payouts: [],
  admins: ['123456789'],
  system: { last_processed_block: 12345 }
};

// Create mock database file
const dbPath = path.join(__dirname, '../wax-bot-main/db.json');
const dbDir = path.dirname(dbPath);

if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

fs.writeFileSync(dbPath, JSON.stringify(mockDbData, null, 2));

console.log('Mock database created successfully!');
console.log('Database path:', dbPath);
console.log('Mock data includes:');
console.log('- 3 trades (2 completed, 1 pending)');
console.log('- 1 blocked memo');
console.log('- Total volume: 350 WAX');
console.log('- Expected profit: 5.25 WAX (1.5%)');

// Test the API logic
function testStatsCalculation() {
  const trades = mockDbData.trades;
  const totalTrades = trades.length;
  const successfulTrades = trades.filter(t => t.status === 'completed').length;
  const pendingTrades = trades.filter(t => t.status === 'pending').length;
  
  const completedTrades = trades.filter(t => t.status === 'completed');
  const totalVolume = completedTrades.reduce((sum, trade) => {
    const amount = parseFloat(trade.quantity.replace(' WAX', ''));
    return sum + amount;
  }, 0);
  
  const totalProfit = totalVolume * 0.015;
  
  console.log('\nStats calculation test:');
  console.log('Total trades:', totalTrades);
  console.log('Successful trades:', successfulTrades);
  console.log('Pending trades:', pendingTrades);
  console.log('Total volume:', totalVolume, 'WAX');
  console.log('Total profit:', totalProfit, 'WAX');
  console.log('Success rate:', ((successfulTrades / totalTrades) * 100).toFixed(2) + '%');
}

testStatsCalculation();
