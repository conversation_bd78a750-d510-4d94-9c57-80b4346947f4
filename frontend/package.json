{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.5", "axios": "^1.11.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "next": "14.2.18", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "14.2.18", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}