'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, Clock, XCircle, ExternalLink } from 'lucide-react';

interface Trade {
  id: number;
  from: string;
  quantity: string;
  memo: string;
  status: string;
  timestamp: string;
  transaction_id: string;
}

const statusIcons = {
  completed: CheckCircle,
  pending: Clock,
  failed: XCircle
};

const statusColors = {
  completed: 'text-green-600 bg-green-100 dark:bg-green-900/20',
  pending: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20',
  failed: 'text-red-600 bg-red-100 dark:bg-red-900/20'
};

export default function RecentTrades() {
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecentTrades();
  }, []);

  const fetchRecentTrades = async () => {
    try {
      const response = await fetch('/api/trades?limit=10&sortBy=timestamp&sortOrder=desc');
      if (response.ok) {
        const data = await response.json();
        setTrades(data.trades || []);
      }
    } catch (error) {
      console.error('Error fetching recent trades:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateAddress = (address: string) => {
    if (address.length <= 12) return address;
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const getWaxScanUrl = (txId: string) => {
    return `https://waxblock.io/transaction/${txId}`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (trades.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">No recent trades found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {trades.map((trade) => {
        const StatusIcon = statusIcons[trade.status as keyof typeof statusIcons] || Clock;
        const statusColor = statusColors[trade.status as keyof typeof statusColors] || statusColors.pending;

        return (
          <div key={trade.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${statusColor}`}>
                <StatusIcon className="w-4 h-4" />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {truncateAddress(trade.from)}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {trade.quantity}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                  <span>{formatDate(trade.timestamp)}</span>
                  {trade.memo && (
                    <>
                      <span>•</span>
                      <span className="truncate max-w-32">{trade.memo}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColor}`}>
                {trade.status}
              </span>
              {trade.transaction_id && (
                <a
                  href={getWaxScanUrl(trade.transaction_id)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  title="View on WAX Block Explorer"
                >
                  <ExternalLink className="w-4 h-4" />
                </a>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
