import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Database connection for JSON database
async function getDbData() {
  const dbPath = path.join(process.cwd(), '../wax-bot-main/db.json');

  try {
    if (!fs.existsSync(dbPath)) {
      // Return empty data structure if database doesn't exist
      return {
        trades: [],
        memos: [],
        pending_payouts: [],
        failed_payouts: [],
        admins: [],
        system: { last_processed_block: 0 }
      };
    }

    const data = fs.readFileSync(dbPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading database:', error);
    return {
      trades: [],
      memos: [],
      pending_payouts: [],
      failed_payouts: [],
      admins: [],
      system: { last_processed_block: 0 }
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const sortBy = searchParams.get('sortBy') || 'timestamp';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    const dbData = await getDbData();
    let trades = dbData.trades || [];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      trades = trades.filter((trade: any) =>
        (trade.memo && trade.memo.toLowerCase().includes(searchLower)) ||
        (trade.from && trade.from.toLowerCase().includes(searchLower)) ||
        (trade.quantity && trade.quantity.toLowerCase().includes(searchLower)) ||
        (trade.transaction_id && trade.transaction_id.toLowerCase().includes(searchLower))
      );
    }

    // Apply status filter
    if (status) {
      trades = trades.filter((trade: any) => trade.status === status);
    }

    // Sort trades
    trades.sort((a: any, b: any) => {
      let aVal = a[sortBy];
      let bVal = b[sortBy];

      // Handle timestamp sorting
      if (sortBy === 'timestamp') {
        aVal = new Date(aVal).getTime();
        bVal = new Date(bVal).getTime();
      }

      if (sortOrder === 'desc') {
        return bVal > aVal ? 1 : -1;
      } else {
        return aVal > bVal ? 1 : -1;
      }
    });

    const total = trades.length;
    const paginatedTrades = trades.slice(offset, offset + limit);

    return NextResponse.json({
      trades: paginatedTrades,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching trades:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trades' },
      { status: 500 }
    );
  }
}
