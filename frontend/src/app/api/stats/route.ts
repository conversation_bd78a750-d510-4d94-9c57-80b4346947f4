import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Database connection for JSON database
async function getDbData() {
  const dbPath = path.join(process.cwd(), '../wax-bot-main/db.json');

  try {
    if (!fs.existsSync(dbPath)) {
      // Return empty data structure if database doesn't exist
      return {
        trades: [],
        memos: [],
        pending_payouts: [],
        failed_payouts: [],
        admins: [],
        system: { last_processed_block: 0 }
      };
    }

    const data = fs.readFileSync(dbPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading database:', error);
    return {
      trades: [],
      memos: [],
      pending_payouts: [],
      failed_payouts: [],
      admins: [],
      system: { last_processed_block: 0 }
    };
  }
}

export async function GET() {
  try {
    const dbData = await getDbData();
    const trades = dbData.trades || [];
    const memos = dbData.memos || [];

    // Get overall statistics
    const totalTrades = trades.length;
    const successfulTrades = trades.filter((t: any) => t.status === 'completed').length;
    const pendingTrades = trades.filter((t: any) => t.status === 'pending').length;
    const failedTrades = trades.filter((t: any) => t.status === 'failed').length;

    // Calculate volume from completed trades
    const completedTrades = trades.filter((t: any) => t.status === 'completed');
    const totalVolume = completedTrades.reduce((sum: number, trade: any) => {
      const amount = parseFloat(trade.quantity?.replace(' WAX', '') || '0');
      return sum + amount;
    }, 0);

    const avgVolume = completedTrades.length > 0 ? totalVolume / completedTrades.length : 0;

    // Get profit calculation (1.5% of total volume)
    const totalProfit = totalVolume * 0.015;

    // Get recent activity (last 24 hours)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentActivity = trades.filter((t: any) => new Date(t.timestamp) > oneDayAgo).length;

    // Get daily stats for the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentTrades = trades.filter((t: any) => new Date(t.timestamp) > thirtyDaysAgo);

    // Group by date
    const dailyStatsMap = new Map();
    recentTrades.forEach((trade: any) => {
      const date = new Date(trade.timestamp).toISOString().split('T')[0];
      if (!dailyStatsMap.has(date)) {
        dailyStatsMap.set(date, { date, trades: 0, successful: 0, volume: 0 });
      }
      const dayStats = dailyStatsMap.get(date);
      dayStats.trades++;
      if (trade.status === 'completed') {
        dayStats.successful++;
        dayStats.volume += parseFloat(trade.quantity?.replace(' WAX', '') || '0');
      }
    });

    const dailyStats = Array.from(dailyStatsMap.values()).sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    // Get top memos
    const memoStats = new Map();
    trades.forEach((trade: any) => {
      if (trade.memo && trade.memo.trim()) {
        const memo = trade.memo.trim();
        if (!memoStats.has(memo)) {
          memoStats.set(memo, { memo, count: 0, volume: 0 });
        }
        const stats = memoStats.get(memo);
        stats.count++;
        if (trade.status === 'completed') {
          stats.volume += parseFloat(trade.quantity?.replace(' WAX', '') || '0');
        }
      }
    });

    const topMemos = Array.from(memoStats.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Get blocked memos count
    const blockedMemos = memos.filter((m: any) => m.is_blocked).length;

    const stats = {
      overview: {
        totalTrades,
        successfulTrades,
        pendingTrades,
        failedTrades,
        successRate: totalTrades > 0 ?
          ((successfulTrades / totalTrades) * 100).toFixed(2) : '0',
        totalVolume,
        averageVolume: avgVolume,
        totalProfit,
        recentActivity,
        blockedMemos
      },
      dailyStats,
      topMemos
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
}
