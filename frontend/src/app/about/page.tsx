import { Shield, Zap, TrendingUp, Users, Code, Globe } from 'lucide-react';

export default function About() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            About Waxify.finance
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Your trusted partner for automated WAX trading with guaranteed returns
          </p>
        </div>

        {/* Mission Statement */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Our Mission</h2>
          <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
            Waxify.finance is dedicated to providing a secure, reliable, and profitable trading experience 
            for WAX blockchain users. Our automated trading bot ensures consistent 1.5% returns on every 
            successful transaction while maintaining the highest standards of security and transparency.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mr-4">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Guaranteed Returns
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              Every successful trade generates a guaranteed 1.5% profit, providing consistent and 
              predictable returns for our users.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Security First
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              Built with robust security measures, including memo filtering and transaction validation 
              to protect against malicious activities.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mr-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Fast Processing
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              Lightning-fast transaction processing with real-time monitoring and instant 
              status updates for all trades.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mr-4">
                <Globe className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Transparent Operations
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300">
              Complete transparency with detailed transaction logs, statistics, and 
              real-time dashboard for monitoring all activities.
            </p>
          </div>
        </div>

        {/* How It Works */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">How It Works</h2>
          
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                1
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Send WAX Tokens
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Transfer your WAX tokens to our trading bot address with an optional memo. 
                  The system automatically detects and processes your transaction.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                2
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Automated Processing
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Our advanced trading algorithms analyze your transaction and apply security 
                  filters to ensure safe processing.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                3
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Profit Calculation
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  A guaranteed 1.5% profit is calculated and added to your original amount, 
                  ensuring you always receive more than you sent.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                4
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Return Payment
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Your original amount plus the 1.5% profit is automatically sent back to 
                  your wallet, completing the trading cycle.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Technical Details</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Blockchain Integration
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Built on WAX blockchain for fast, low-cost transactions</li>
                <li>• Real-time transaction monitoring and validation</li>
                <li>• Secure wallet integration with multi-signature support</li>
                <li>• Automated smart contract interactions</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Security Features
              </h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li>• Advanced memo filtering and blacklist protection</li>
                <li>• Transaction amount validation and limits</li>
                <li>• Automated fraud detection systems</li>
                <li>• Comprehensive audit logs and monitoring</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Get Started Today</h2>
          <p className="text-blue-100 mb-6 text-lg">
            Join thousands of users who trust Waxify.finance for their WAX trading needs
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/dashboard"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
            >
              <TrendingUp className="w-5 h-5 mr-2" />
              View Dashboard
            </a>
            <a
              href="/explorer"
              className="inline-flex items-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
            >
              <Users className="w-5 h-5 mr-2" />
              Explore Trades
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
