'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Activity, DollarSign, Users, Clock } from 'lucide-react';
import StatsCard from '@/components/StatsCard';
import TradeChart from '@/components/TradeChart';
import RecentTrades from '@/components/RecentTrades';

interface Stats {
  overview: {
    totalTrades: number;
    successfulTrades: number;
    pendingTrades: number;
    failedTrades: number;
    successRate: string;
    totalVolume: number;
    averageVolume: number;
    totalProfit: number;
    recentActivity: number;
    blockedMemos: number;
  };
  dailyStats: Array<{
    date: string;
    trades: number;
    successful: number;
    volume: number;
  }>;
  topMemos: Array<{
    memo: string;
    count: number;
    volume: number;
  }>;
}

export default function Dashboard() {
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch statistics');
      }
      const data = await response.json();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Error Loading Dashboard</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={fetchStats}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Trading Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Monitor your WAX trading bot performance and statistics
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Trades"
            value={stats.overview.totalTrades.toLocaleString()}
            icon={Activity}
            color="blue"
            change={`${stats.overview.recentActivity} in 24h`}
          />
          <StatsCard
            title="Success Rate"
            value={`${stats.overview.successRate}%`}
            icon={TrendingUp}
            color="green"
            change={`${stats.overview.successfulTrades}/${stats.overview.totalTrades} trades`}
          />
          <StatsCard
            title="Total Volume"
            value={`${stats.overview.totalVolume.toFixed(2)} WAX`}
            icon={DollarSign}
            color="purple"
            change={`Avg: ${stats.overview.averageVolume.toFixed(2)} WAX`}
          />
          <StatsCard
            title="Total Profit"
            value={`${stats.overview.totalProfit.toFixed(2)} WAX`}
            icon={TrendingUp}
            color="green"
            change="1.5% margin"
          />
        </div>

        {/* Secondary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <StatsCard
            title="Pending Trades"
            value={stats.overview.pendingTrades.toString()}
            icon={Clock}
            color="yellow"
            change="Processing"
          />
          <StatsCard
            title="Failed Trades"
            value={stats.overview.failedTrades.toString()}
            icon={TrendingDown}
            color="red"
            change="Blocked/Failed"
          />
          <StatsCard
            title="Blocked Memos"
            value={stats.overview.blockedMemos.toString()}
            icon={Users}
            color="gray"
            change="Security filters"
          />
        </div>

        {/* Charts and Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Trade Volume Chart */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Daily Trading Volume (Last 30 Days)
            </h3>
            <TradeChart data={stats.dailyStats} />
          </div>

          {/* Recent Trades */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Recent Activity
            </h3>
            <RecentTrades />
          </div>
        </div>

        {/* Top Memos Table */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Trading Memos
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Memo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Trade Count
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Total Volume
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {stats.topMemos.map((memo, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {memo.memo || 'No memo'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {memo.count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {memo.volume.toFixed(2)} WAX
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
